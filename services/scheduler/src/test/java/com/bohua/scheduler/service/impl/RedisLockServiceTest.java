package com.bohua.scheduler.service.impl;

import com.bohua.scheduler.service.LockService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis分布式锁服务测试
 */
@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
class RedisLockServiceTest {
    
    @Container
    static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:7-alpine"))
            .withExposedPorts(6379);
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.redis.host", redis::getHost);
        registry.add("spring.redis.port", redis::getFirstMappedPort);
        registry.add("scheduler.lock.type", () -> "redis");
    }
    
    @Autowired
    private LockService lockService;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private RedisLockService redisLockService;
    
    @BeforeEach
    void setUp() {
        // 清理Redis中的测试数据
        redisTemplate.getConnectionFactory().getConnection().flushAll();
        
        // 确保注入的是RedisLockService实例
        assertTrue(lockService instanceof RedisLockService);
        redisLockService = (RedisLockService) lockService;
    }
    
    @Test
    void testTryLock_Success() {
        String lockKey = "test-lock-1";
        
        // 测试获取锁成功
        boolean acquired = lockService.tryLock(lockKey, 10);
        assertTrue(acquired);
        
        // 验证锁确实存在
        assertTrue(redisLockService.isLocked(lockKey));
        
        // 释放锁
        lockService.unlock(lockKey);
        
        // 验证锁已释放
        assertFalse(redisLockService.isLocked(lockKey));
    }
    
    @Test
    void testTryLock_Conflict() {
        String lockKey = "test-lock-2";
        
        // 第一次获取锁成功
        boolean firstAcquired = lockService.tryLock(lockKey, 10);
        assertTrue(firstAcquired);
        
        // 第二次获取同一个锁失败
        boolean secondAcquired = lockService.tryLock(lockKey, 1);
        assertFalse(secondAcquired);
        
        // 释放锁
        lockService.unlock(lockKey);
        
        // 释放后可以重新获取
        boolean thirdAcquired = lockService.tryLock(lockKey, 10);
        assertTrue(thirdAcquired);
        
        lockService.unlock(lockKey);
    }
    
    @Test
    void testLockExpiration() throws InterruptedException {
        String lockKey = "test-lock-3";
        
        // 获取一个短期锁（2秒）
        boolean acquired = lockService.tryLock(lockKey, 2);
        assertTrue(acquired);
        
        // 验证锁存在
        assertTrue(redisLockService.isLocked(lockKey));
        
        // 等待锁过期
        Thread.sleep(3000);
        
        // 验证锁已过期
        assertFalse(redisLockService.isLocked(lockKey));
        
        // 可以重新获取锁
        boolean reacquired = lockService.tryLock(lockKey, 10);
        assertTrue(reacquired);
        
        lockService.unlock(lockKey);
    }
    
    @Test
    void testExecuteWithLock() throws Exception {
        String lockKey = "test-lock-4";
        AtomicInteger counter = new AtomicInteger(0);
        
        // 使用executeWithLock执行操作
        Integer result = lockService.executeWithLock(lockKey, 10, () -> {
            counter.incrementAndGet();
            return counter.get();
        });
        
        assertEquals(1, result.intValue());
        assertEquals(1, counter.get());
        
        // 验证锁已自动释放
        assertFalse(redisLockService.isLocked(lockKey));
    }
    
    @Test
    void testConcurrentLocking() throws InterruptedException {
        String lockKey = "test-lock-5";
        int threadCount = 10;
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger sharedCounter = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        // 启动多个线程同时尝试获取锁
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    if (lockService.tryLock(lockKey, 5)) {
                        successCount.incrementAndGet();
                        
                        // 模拟临界区操作
                        int current = sharedCounter.get();
                        Thread.sleep(100); // 模拟处理时间
                        sharedCounter.set(current + 1);
                        
                        lockService.unlock(lockKey);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        assertTrue(latch.await(30, TimeUnit.SECONDS));
        executor.shutdown();
        
        // 验证只有一个线程成功获取锁
        assertEquals(1, successCount.get());
        assertEquals(1, sharedCounter.get());
        
        // 验证锁已释放
        assertFalse(redisLockService.isLocked(lockKey));
    }
    
    @Test
    void testRenewLock() throws InterruptedException {
        String lockKey = "test-lock-6";
        
        // 获取锁
        boolean acquired = lockService.tryLock(lockKey, 3);
        assertTrue(acquired);
        
        // 获取初始TTL
        long initialTtl = redisLockService.getLockTtl(lockKey);
        assertTrue(initialTtl > 0);
        
        // 等待1秒
        Thread.sleep(1000);
        
        // 续期锁
        boolean renewed = redisLockService.renewLock(lockKey, 10);
        assertTrue(renewed);
        
        // 验证TTL已更新
        long newTtl = redisLockService.getLockTtl(lockKey);
        assertTrue(newTtl > initialTtl);
        
        lockService.unlock(lockKey);
    }
    
    @Test
    void testExecuteWithLock_Exception() {
        String lockKey = "test-lock-7";
        
        // 测试执行过程中抛出异常，锁仍能正确释放
        assertThrows(RuntimeException.class, () -> {
            lockService.executeWithLock(lockKey, 10, () -> {
                throw new RuntimeException("Test exception");
            });
        });
        
        // 验证锁已释放
        assertFalse(redisLockService.isLocked(lockKey));
        
        // 可以重新获取锁
        boolean reacquired = lockService.tryLock(lockKey, 10);
        assertTrue(reacquired);
        
        lockService.unlock(lockKey);
    }
}
