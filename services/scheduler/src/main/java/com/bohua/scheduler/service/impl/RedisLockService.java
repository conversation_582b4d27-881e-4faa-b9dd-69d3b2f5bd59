package com.bohua.scheduler.service.impl;

import com.bohua.scheduler.service.LockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁实现
 * 适用于多实例部署模式
 */
@Service
@Slf4j
@ConditionalOnProperty(name = "scheduler.lock.type", havingValue = "redis")
public class RedisLockService implements LockService {
    
    private static final String LOCK_PREFIX = "scheduler:lock:";
    private static final String LOCK_VALUE_PREFIX = "lock_value_";
    
    // Lua脚本：原子性释放锁
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";
    
    // Lua脚本：原子性续期锁
    private static final String RENEW_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('expire', KEYS[1], ARGV[2]) " +
        "else " +
        "    return 0 " +
        "end";
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private final DefaultRedisScript<Long> unlockScript;
    private final DefaultRedisScript<Long> renewScript;
    
    // 线程本地存储，保存当前线程持有的锁值
    private final ThreadLocal<String> lockValueHolder = new ThreadLocal<>();
    
    public RedisLockService() {
        this.unlockScript = new DefaultRedisScript<>();
        this.unlockScript.setScriptText(UNLOCK_SCRIPT);
        this.unlockScript.setResultType(Long.class);
        
        this.renewScript = new DefaultRedisScript<>();
        this.renewScript.setScriptText(RENEW_SCRIPT);
        this.renewScript.setResultType(Long.class);
    }
    
    @Override
    public boolean tryLock(String lockKey, long timeoutSeconds) {
        String redisKey = LOCK_PREFIX + lockKey;
        String lockValue = generateLockValue();
        
        try {
            // 使用SET命令的NX和EX选项实现原子性加锁
            Boolean result = redisTemplate.opsForValue()
                .setIfAbsent(redisKey, lockValue, timeoutSeconds, TimeUnit.SECONDS);
            
            if (Boolean.TRUE.equals(result)) {
                // 成功获取锁，保存锁值到线程本地存储
                lockValueHolder.set(lockValue);
                log.debug("成功获取Redis分布式锁: lockKey={}, lockValue={}, timeout={}s", 
                         lockKey, lockValue, timeoutSeconds);
                return true;
            } else {
                log.debug("获取Redis分布式锁失败: lockKey={}, timeout={}s", lockKey, timeoutSeconds);
                return false;
            }
        } catch (Exception e) {
            log.error("获取Redis分布式锁异常: lockKey={}", lockKey, e);
            return false;
        }
    }
    
    @Override
    public void unlock(String lockKey) {
        String redisKey = LOCK_PREFIX + lockKey;
        String lockValue = lockValueHolder.get();
        
        if (lockValue == null) {
            log.warn("尝试释放未持有的锁: lockKey={}", lockKey);
            return;
        }
        
        try {
            // 使用Lua脚本原子性释放锁
            Long result = redisTemplate.execute(unlockScript, 
                Collections.singletonList(redisKey), lockValue);
            
            if (result != null && result == 1) {
                log.debug("成功释放Redis分布式锁: lockKey={}, lockValue={}", lockKey, lockValue);
            } else {
                log.warn("释放Redis分布式锁失败，锁可能已过期或被其他线程持有: lockKey={}, lockValue={}", 
                        lockKey, lockValue);
            }
        } catch (Exception e) {
            log.error("释放Redis分布式锁异常: lockKey={}, lockValue={}", lockKey, lockValue, e);
        } finally {
            // 清理线程本地存储
            lockValueHolder.remove();
        }
    }
    
    @Override
    public <T> T executeWithLock(String lockKey, long timeoutSeconds, LockAction<T> action) throws Exception {
        if (!tryLock(lockKey, timeoutSeconds)) {
            throw new RuntimeException("获取分布式锁失败: " + lockKey);
        }
        
        try {
            return action.execute();
        } finally {
            unlock(lockKey);
        }
    }
    
    /**
     * 续期锁（可选功能，用于长时间运行的任务）
     */
    public boolean renewLock(String lockKey, long timeoutSeconds) {
        String redisKey = LOCK_PREFIX + lockKey;
        String lockValue = lockValueHolder.get();
        
        if (lockValue == null) {
            log.warn("尝试续期未持有的锁: lockKey={}", lockKey);
            return false;
        }
        
        try {
            Long result = redisTemplate.execute(renewScript, 
                Collections.singletonList(redisKey), lockValue, String.valueOf(timeoutSeconds));
            
            if (result != null && result == 1) {
                log.debug("成功续期Redis分布式锁: lockKey={}, lockValue={}, timeout={}s", 
                         lockKey, lockValue, timeoutSeconds);
                return true;
            } else {
                log.warn("续期Redis分布式锁失败: lockKey={}, lockValue={}", lockKey, lockValue);
                return false;
            }
        } catch (Exception e) {
            log.error("续期Redis分布式锁异常: lockKey={}, lockValue={}", lockKey, lockValue, e);
            return false;
        }
    }
    
    /**
     * 生成唯一的锁值
     */
    private String generateLockValue() {
        return LOCK_VALUE_PREFIX + Thread.currentThread().getId() + "_" + UUID.randomUUID().toString();
    }
    
    /**
     * 检查锁是否存在
     */
    public boolean isLocked(String lockKey) {
        String redisKey = LOCK_PREFIX + lockKey;
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(redisKey));
        } catch (Exception e) {
            log.error("检查锁状态异常: lockKey={}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 获取锁的剩余过期时间（秒）
     */
    public long getLockTtl(String lockKey) {
        String redisKey = LOCK_PREFIX + lockKey;
        try {
            Long ttl = redisTemplate.getExpire(redisKey, TimeUnit.SECONDS);
            return ttl != null ? ttl : -1;
        } catch (Exception e) {
            log.error("获取锁TTL异常: lockKey={}", lockKey, e);
            return -1;
        }
    }
}
