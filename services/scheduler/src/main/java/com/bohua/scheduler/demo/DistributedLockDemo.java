package com.bohua.scheduler.demo;

import com.bohua.scheduler.service.LockService;
import com.bohua.scheduler.service.impl.RedisLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 分布式锁演示程序
 * 仅在启用Redis锁且demo模式时运行
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "scheduler.demo.enabled", havingValue = "true")
public class DistributedLockDemo implements CommandLineRunner {
    
    @Autowired
    private LockService lockService;
    
    private final AtomicInteger counter = new AtomicInteger(0);
    
    @Override
    public void run(String... args) throws Exception {
        log.info("=== 分布式锁演示开始 ===");
        
        // 检查锁服务类型
        if (lockService instanceof RedisLockService) {
            log.info("使用Redis分布式锁");
            demonstrateRedisLock();
        } else {
            log.info("使用本地锁");
            demonstrateLocalLock();
        }
        
        log.info("=== 分布式锁演示结束 ===");
    }
    
    /**
     * 演示Redis分布式锁
     */
    private void demonstrateRedisLock() throws InterruptedException {
        RedisLockService redisLockService = (RedisLockService) lockService;
        
        // 1. 基本锁操作演示
        demonstrateBasicLocking();
        
        // 2. 并发锁竞争演示
        demonstrateConcurrentLocking();
        
        // 3. 锁续期演示
        demonstrateLockRenewal(redisLockService);
        
        // 4. executeWithLock演示
        demonstrateExecuteWithLock();
    }
    
    /**
     * 演示本地锁
     */
    private void demonstrateLocalLock() throws InterruptedException {
        log.info("本地锁演示 - 仅支持单实例");
        demonstrateBasicLocking();
        demonstrateConcurrentLocking();
        demonstrateExecuteWithLock();
    }
    
    /**
     * 基本锁操作演示
     */
    private void demonstrateBasicLocking() {
        log.info("--- 基本锁操作演示 ---");
        String lockKey = "demo-basic-lock";
        
        // 获取锁
        boolean acquired = lockService.tryLock(lockKey, 10);
        log.info("尝试获取锁 '{}': {}", lockKey, acquired ? "成功" : "失败");
        
        if (acquired) {
            try {
                // 模拟业务操作
                log.info("执行业务操作...");
                Thread.sleep(1000);
                log.info("业务操作完成");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                // 释放锁
                lockService.unlock(lockKey);
                log.info("释放锁 '{}'", lockKey);
            }
        }
    }
    
    /**
     * 并发锁竞争演示
     */
    private void demonstrateConcurrentLocking() throws InterruptedException {
        log.info("--- 并发锁竞争演示 ---");
        String lockKey = "demo-concurrent-lock";
        int threadCount = 5;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        counter.set(0);
        
        // 启动多个线程竞争同一个锁
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i + 1;
            executor.submit(() -> {
                boolean acquired = lockService.tryLock(lockKey, 5);
                if (acquired) {
                    try {
                        log.info("线程 {} 获取锁成功，开始执行任务", threadId);
                        
                        // 模拟临界区操作
                        int current = counter.get();
                        Thread.sleep(500); // 模拟处理时间
                        counter.set(current + 1);
                        
                        log.info("线程 {} 完成任务，计数器值: {}", threadId, counter.get());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        lockService.unlock(lockKey);
                        log.info("线程 {} 释放锁", threadId);
                    }
                } else {
                    log.info("线程 {} 获取锁失败", threadId);
                }
            });
        }
        
        executor.shutdown();
        executor.awaitTermination(30, TimeUnit.SECONDS);
        
        log.info("并发测试完成，最终计数器值: {} (应该为1)", counter.get());
    }
    
    /**
     * 锁续期演示
     */
    private void demonstrateLockRenewal(RedisLockService redisLockService) throws InterruptedException {
        log.info("--- 锁续期演示 ---");
        String lockKey = "demo-renewal-lock";
        
        // 获取一个短期锁
        boolean acquired = lockService.tryLock(lockKey, 3);
        if (acquired) {
            try {
                log.info("获取锁成功，初始TTL: {} 秒", redisLockService.getLockTtl(lockKey));
                
                // 等待2秒
                Thread.sleep(2000);
                log.info("等待2秒后，TTL: {} 秒", redisLockService.getLockTtl(lockKey));
                
                // 续期锁
                boolean renewed = redisLockService.renewLock(lockKey, 10);
                log.info("续期锁: {}, 新TTL: {} 秒", renewed ? "成功" : "失败", 
                        redisLockService.getLockTtl(lockKey));
                
            } finally {
                lockService.unlock(lockKey);
                log.info("释放锁");
            }
        }
    }
    
    /**
     * executeWithLock演示
     */
    private void demonstrateExecuteWithLock() {
        log.info("--- executeWithLock演示 ---");
        String lockKey = "demo-execute-lock";
        
        try {
            String result = lockService.executeWithLock(lockKey, 10, () -> {
                log.info("在锁保护下执行操作");
                Thread.sleep(1000);
                return "操作完成，时间: " + System.currentTimeMillis();
            });
            
            log.info("executeWithLock结果: {}", result);
        } catch (Exception e) {
            log.error("executeWithLock执行失败", e);
        }
    }
}
