#!/bin/bash

# 启动调度器并使用Redis分布式锁的脚本

set -e

echo "=== 启动CV调度器（Redis分布式锁模式） ==="

# 检查Redis是否可用
check_redis() {
    local redis_host=${REDIS_HOST:-localhost}
    local redis_port=${REDIS_PORT:-6379}
    
    echo "检查Redis连接: $redis_host:$redis_port"
    
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli -h "$redis_host" -p "$redis_port" ping >/dev/null 2>&1; then
            echo "✅ Redis连接正常"
            return 0
        else
            echo "❌ Redis连接失败"
            return 1
        fi
    else
        echo "⚠️  redis-cli未安装，跳过Redis连接检查"
        return 0
    fi
}

# 设置环境变量
export SCHEDULER_LOCK_TYPE=redis
export SPRING_PROFILES_ACTIVE=redis

# 如果没有设置Redis配置，使用默认值
export REDIS_HOST=${REDIS_HOST:-localhost}
export REDIS_PORT=${REDIS_PORT:-6379}
export REDIS_PASSWORD=${REDIS_PASSWORD:-}
export REDIS_DATABASE=${REDIS_DATABASE:-0}

echo "配置信息:"
echo "  锁类型: $SCHEDULER_LOCK_TYPE"
echo "  Spring Profile: $SPRING_PROFILES_ACTIVE"
echo "  Redis主机: $REDIS_HOST"
echo "  Redis端口: $REDIS_PORT"
echo "  Redis数据库: $REDIS_DATABASE"

# 检查Redis连接
if ! check_redis; then
    echo ""
    echo "Redis连接检查失败，请确保Redis服务正在运行："
    echo "  docker run -d --name redis -p 6379:6379 redis:7-alpine"
    echo "  或者"
    echo "  docker-compose up redis"
    echo ""
    read -p "是否继续启动？(y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 构建项目（如果需要）
if [[ "${BUILD:-true}" == "true" ]]; then
    echo ""
    echo "构建项目..."
    mvn clean package -DskipTests
fi

# 启动应用
echo ""
echo "启动调度器..."
java -jar target/cv-scheduler-1.0.0.jar \
    --scheduler.lock.type=redis \
    --spring.profiles.active=redis \
    --scheduler.demo.enabled=true

echo ""
echo "调度器已停止"
