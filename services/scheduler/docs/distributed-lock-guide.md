# 分布式锁使用指南

## 概述

调度器现在支持两种锁实现：
- **本地锁（LocalLockService）**：适用于单实例部署
- **Redis分布式锁（RedisLockService）**：适用于多实例部署

## 配置方式

### 1. 使用本地锁（默认）

```yaml
scheduler:
  lock:
    type: local
```

### 2. 使用Redis分布式锁

#### 方式一：修改配置文件

```yaml
scheduler:
  lock:
    type: redis

spring:
  redis:
    host: localhost
    port: 6379
    password: your-password
    database: 0
```

#### 方式二：使用环境变量

```bash
export SCHEDULER_LOCK_TYPE=redis
export REDIS_HOST=your-redis-host
export REDIS_PORT=6379
export REDIS_PASSWORD=your-password
```

#### 方式三：使用专用配置文件

```bash
java -jar scheduler.jar --spring.profiles.active=redis
```

## Redis分布式锁特性

### 核心功能

1. **原子性操作**：使用Redis的SET NX EX命令确保加锁的原子性
2. **自动过期**：支持锁超时自动释放，防止死锁
3. **安全释放**：使用Lua脚本确保只有锁持有者才能释放锁
4. **锁续期**：支持长时间任务的锁续期功能

### 高级功能

1. **锁状态检查**：`isLocked(lockKey)` - 检查锁是否存在
2. **TTL查询**：`getLockTtl(lockKey)` - 获取锁的剩余过期时间
3. **锁续期**：`renewLock(lockKey, timeoutSeconds)` - 延长锁的过期时间

## 使用示例

### 基本用法

```java
@Autowired
private LockService lockService;

// 尝试获取锁
if (lockService.tryLock("my-lock", 30)) {
    try {
        // 执行需要同步的操作
        doSomething();
    } finally {
        // 释放锁
        lockService.unlock("my-lock");
    }
}
```

### 使用executeWithLock

```java
// 自动管理锁的生命周期
String result = lockService.executeWithLock("my-lock", 30, () -> {
    // 执行需要同步的操作
    return doSomethingAndReturnResult();
});
```

### 长时间任务的锁续期

```java
@Autowired
private RedisLockService redisLockService;

if (lockService.tryLock("long-task-lock", 60)) {
    try {
        for (int i = 0; i < 100; i++) {
            // 执行长时间任务
            doLongRunningTask(i);
            
            // 每10次迭代续期一次锁
            if (i % 10 == 0) {
                redisLockService.renewLock("long-task-lock", 60);
            }
        }
    } finally {
        lockService.unlock("long-task-lock");
    }
}
```

## 性能对比

| 特性 | 本地锁 | Redis分布式锁 |
|------|--------|---------------|
| 延迟 | 极低（纳秒级） | 低（毫秒级） |
| 吞吐量 | 极高 | 高 |
| 多实例支持 | ❌ | ✅ |
| 网络依赖 | ❌ | ✅ |
| 故障恢复 | 进程重启后丢失 | 自动过期恢复 |

## 最佳实践

### 1. 锁超时设置

- **短任务**：设置为任务预期执行时间的2-3倍
- **长任务**：使用较短的初始超时时间，配合锁续期机制

```java
// 短任务
lockService.tryLock("quick-task", 10);

// 长任务
lockService.tryLock("long-task", 30);
// 定期续期
redisLockService.renewLock("long-task", 30);
```

### 2. 锁粒度控制

```java
// 粗粒度锁 - 适用于简单场景
lockService.tryLock("global-lock", 30);

// 细粒度锁 - 适用于高并发场景
lockService.tryLock("user-" + userId, 30);
lockService.tryLock("service-" + serviceId, 30);
```

### 3. 异常处理

```java
String lockKey = "my-lock";
boolean acquired = false;

try {
    acquired = lockService.tryLock(lockKey, 30);
    if (!acquired) {
        throw new RuntimeException("获取锁失败");
    }
    
    // 执行业务逻辑
    doBusinessLogic();
    
} catch (Exception e) {
    log.error("业务执行失败", e);
    throw e;
} finally {
    if (acquired) {
        lockService.unlock(lockKey);
    }
}
```

## 监控和调试

### 日志配置

```yaml
logging:
  level:
    com.bohua.scheduler.service.impl.RedisLockService: DEBUG
```

### 关键日志

- 锁获取成功：`成功获取Redis分布式锁: lockKey=xxx`
- 锁获取失败：`获取Redis分布式锁失败: lockKey=xxx`
- 锁释放成功：`成功释放Redis分布式锁: lockKey=xxx`
- 锁续期成功：`成功续期Redis分布式锁: lockKey=xxx`

### Redis监控

```bash
# 查看所有锁
redis-cli KEYS "scheduler:lock:*"

# 查看特定锁的TTL
redis-cli TTL "scheduler:lock:my-lock"

# 查看锁的值
redis-cli GET "scheduler:lock:my-lock"
```

## 故障排除

### 常见问题

1. **锁获取失败**
   - 检查Redis连接状态
   - 确认锁是否被其他实例持有
   - 检查锁超时设置是否合理

2. **锁释放失败**
   - 检查锁是否已过期
   - 确认当前线程是否为锁持有者
   - 检查Redis连接状态

3. **性能问题**
   - 优化锁粒度
   - 减少锁持有时间
   - 考虑使用本地锁（单实例场景）

### 切换锁类型

```bash
# 从本地锁切换到Redis锁
export SCHEDULER_LOCK_TYPE=redis

# 从Redis锁切换到本地锁
export SCHEDULER_LOCK_TYPE=local
```

重启应用后生效，无需修改代码。
